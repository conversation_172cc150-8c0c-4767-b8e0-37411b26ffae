package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

// RouterConfig holds all handlers needed for routing
type RouterConfig struct {
	UserHandler           *handlers.UserHandler
	UserTypeHandler       *handlers.UserTypeHandler
	WorkspaceHandler      *handlers.WorkspaceHandler
	ClusterHandler        *handlers.ClusterHandler
	CloudflareHandler     *handlers.CloudflareHandler
	NamespaceHandler      *handlers.NamespaceHandler
	DomainHandler         *handlers.DomainHandler
	DeploymentHandler     *handlers.DeploymentHandler
	EnvironmentHandler    *handlers.EnvironmentHandler
	ServiceHandler        *handlers.ServiceHandler
	IngressHandler        *handlers.IngressHandler
	IngressSpecHandler    *handlers.IngressSpecHandler
	ProjectHandler        *handlers.ProjectHandler
	JobHandler            *handlers.JobHandler
	JobLogHandler         *handlers.JobLogHandler
	OperationHandler      *handlers.OperationHandler
	HealthHandler         *handlers.HealthHandler
	ServerStatusHandler   *handlers.ServerStatusHandler
	JobStatusHandler      *handlers.JobStatusHandler
	DnsHandler            *handlers.DnsHandler
	OrderHandler          *handlers.OrderHandler
	OrderDomainHandler    *handlers.OrderDomainHandler
	OrderNamespaceHandler *handlers.OrderNamespaceHandler
	AuthMiddleware        fiber.Handler
}

// SetupRoutes configures all application routes
func SetupRoutes(app *fiber.App, config *RouterConfig) {
	// Global middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "*",
	}))

	// Health check routes (no prefix, direct access)
	SetupHealthRoutes(app, config.HealthHandler)

	// API v1 routes
	api := app.Group("/api/v1")

	// Authentication routes (no auth middleware)
	SetupAuthRoutes(api, config.UserHandler)

	// Protected routes (require authentication)
	protected := api.Use(config.AuthMiddleware)

	// User management routes
	SetupUserRoutes(protected, config.UserHandler)

	// User type management routes
	SetupUserTypeRoutes(protected, config.UserTypeHandler)

	// Workspace management routes
	SetupWorkspaceRoutes(protected, config.WorkspaceHandler)

	// Cluster management routes
	SetupClusterRoutes(protected, config.ClusterHandler)

	// Cloudflare routes
	SetupCloudflareRoutes(protected, config.CloudflareHandler)

	// Namespace management routes
	SetupNamespaceRoutes(protected, config.NamespaceHandler)

	// Template management routes
	SetupTemplateRoutes(protected, config.NamespaceHandler)

	// Domain management routes
	SetupDomainRoutes(protected, config.DomainHandler)

	// Deployment management routes
	SetupDeploymentRoutes(protected, config.DeploymentHandler)

	// Environment management routes
	RegisterEnvironmentRoutes(protected, config.EnvironmentHandler)

	// Service management routes
	RegisterServiceRoutes(protected, config.ServiceHandler)

	// Ingress management routes
	RegisterIngressRoutes(protected, config.IngressHandler)

	// Ingress Spec management routes
	RegisterIngressSpecRoutes(protected, config.IngressSpecHandler)

	// Project management routes
	SetupProjectRoutes(protected, config.ProjectHandler)

	// Job management routes
	SetupJobRoutes(protected, config.JobHandler)

	// Job log routes
	SetupJobLogRoutes(protected, config.JobLogHandler)

	// Operation management routes
	SetupOperationRoutes(protected, config.OperationHandler)

	// Server status management routes
	SetupServerStatusRoutes(protected, config.ServerStatusHandler)

	// Job status management routes
	SetupJobStatusRoutes(protected, config.JobStatusHandler)

	// DNS management routes
	SetupDnsRoutes(protected, config.OperationHandler)

	// Order management routes
	SetupOrderRoutes(protected, config.OrderHandler)

	// Order Domain management routes
	SetupOrderDomainRoutes(protected, config.OrderDomainHandler)

	// Order Namespace management routes
	SetupOrderNamespaceRoutes(protected, config.OrderNamespaceHandler)
}

// SetupAuthRoutes sets up authentication routes
func SetupAuthRoutes(api fiber.Router, userHandler *handlers.UserHandler) {
	auth := api.Group("/auth")
	auth.Post("/register", userHandler.Register)
	auth.Post("/login", userHandler.Login)
	auth.Post("/register-sale", userHandler.CreateSaleUser) // Public - create sale user
}
