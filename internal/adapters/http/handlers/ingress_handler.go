package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type IngressHandler struct {
	ingressService ports.IngressService
}

func NewIngressHandler(ingressService ports.IngressService) *IngressHandler {
	return &IngressHandler{
		ingressService: ingressService,
	}
}

func (h *IngressHandler) CreateIngress(c *fiber.Ctx) error {
	var req dto.CreateIngressRequest
	if err := c.BodyP<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	ingress, err := h.ingressService.Create(req.Name, req.Class, req.NamespaceID)
	if err != nil {
		if strings.Contains(err.<PERSON><PERSON>r(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.<PERSON>rror())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Ingress created successfully", dto.ToIngressDetailDTO(ingress))
}

func (h *IngressHandler) GetIngresses(c *fiber.Ctx) error {
	filter := &ports.IngressFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid namespace_id parameter")
		}
		filter.NamespaceID = &namespaceID
	}

	ingresses, err := h.ingressService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var ingressDTOs []*dto.IngressListItemResponse
	for _, ingress := range ingresses {
		ingressDTOs = append(ingressDTOs, dto.ToIngressListItemDTO(ingress))
	}

	return response.Success(c, fiber.StatusOK, "Ingresses retrieved successfully", ingressDTOs)
}

func (h *IngressHandler) GetIngressByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	ingress, err := h.ingressService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress retrieved successfully", dto.ToIngressDetailDTO(ingress))
}

func (h *IngressHandler) UpdateIngress(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateIngressRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	ingress, err := h.ingressService.Update(id, req.Name, req.Class, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress updated successfully", dto.ToIngressDetailDTO(ingress))
}

func (h *IngressHandler) DeleteIngress(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.ingressService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress deleted successfully", nil)
}
