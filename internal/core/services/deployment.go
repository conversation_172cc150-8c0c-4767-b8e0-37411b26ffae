package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"time"
)

type DeploymentService struct {
	deploymentRepo ports.DeploymentRepository
	namespaceRepo  ports.NamespaceRepository
	userRepo       ports.UserRepository
}

func NewDeploymentService(deploymentRepo ports.DeploymentRepository, namespaceRepo ports.NamespaceRepository, userRepo ports.UserRepository) ports.DeploymentService {
	return &DeploymentService{
		deploymentRepo: deploymentRepo,
		namespaceRepo:  namespaceRepo,
		userRepo:       userRepo,
	}
}

func (s *DeploymentService) Create(name, image string, containerPort, replicas, namespaceID uint64) (*domain.Deployment, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if image == "" {
		return nil, errors.New("image is required")
	}
	if containerPort == 0 {
		return nil, errors.New("container port must be greater than 0")
	}
	if replicas == 0 {
		return nil, errors.New("replicas must be greater than 0")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}
	now := time.Now()
	deployment := &domain.Deployment{
		Name:           name,
		Image:          image,
		ContainerPort:  containerPort,
		Replicas:       replicas, // Default to 1 replica
		NamespaceID:    namespaceID,
		StatusID:       1, // Default status
		LastDeployedAt: &now,
	}

	if err := s.deploymentRepo.Insert(deployment); err != nil {
		return nil, err
	}

	return deployment, nil
}

func (s *DeploymentService) GetAll(filter *ports.DeploymentFilter) ([]*domain.Deployment, error) {
	return s.deploymentRepo.FindAll(filter)
}

func (s *DeploymentService) GetByID(id uint64) (*domain.Deployment, error) {
	if id == 0 {
		return nil, errors.New("invalid ID")
	}

	return s.deploymentRepo.FindByID(id)
}

func (s *DeploymentService) Update(id uint64, name, image string, containerPort, replicas, namespaceID, statusID uint64) (*domain.Deployment, error) {
	if id == 0 {
		return nil, errors.New("invalid ID")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if image == "" {
		return nil, errors.New("image is required")
	}
	if containerPort == 0 {
		return nil, errors.New("container port must be greater than 0")
	}
	if replicas == 0 {
		return nil, errors.New("replicas must be greater than 0")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify deployment exists
	deployment, err := s.deploymentRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("deployment not found")
	}

	// Verify namespace exists
	_, err = s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	status := statusID
	if deployment.StatusID == 1 {
		status = 1 // automatically set to "maintenance" if not 1
	}

	deployment.Name = name
	deployment.Image = image
	deployment.ContainerPort = containerPort
	deployment.Replicas = replicas
	deployment.NamespaceID = namespaceID
	deployment.StatusID = status

	if err := s.deploymentRepo.Update(deployment); err != nil {
		return nil, err
	}

	return deployment, nil
}

func (s *DeploymentService) UpdateStatus(id uint64, statusID uint64) (*domain.Deployment, error) {
	if id == 0 {
		return nil, errors.New("invalid ID")
	}
	if statusID == 0 {
		return nil, errors.New("status ID is required")
	}

	// Verify deployment exists
	deployment, err := s.deploymentRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("deployment not found")
	}

	if deployment.StatusID == 1 {
		statusID = 1
	}

	// Update only the status
	deployment.StatusID = statusID

	if err := s.deploymentRepo.Update(deployment); err != nil {
		return nil, err
	}

	return deployment, nil
}

func (s *DeploymentService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("invalid ID")
	}

	// Verify deployment exists
	_, err := s.deploymentRepo.FindByID(id)
	if err != nil {
		return errors.New("deployment not found")
	}

	return s.deploymentRepo.Delete(id)
}
