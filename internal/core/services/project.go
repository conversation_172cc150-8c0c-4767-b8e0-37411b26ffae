package services

import (
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/slug"
	"os"
	"strconv"
)

type ProjectService struct {
	namespaceService      ports.NamespaceService
	deploymentService     ports.DeploymentService
	environmentService    ports.EnvironmentService
	serviceService        ports.ServiceService
	ingressService        ports.IngressService
	ingressSpecService    ports.IngressSpecService
	clusterService        ports.ClusterService
	orderService          ports.OrderService
	orderNamespaceService ports.OrderNamespaceService
	domainService         ports.DomainService
}

func NewProjectService(
	namespaceService ports.NamespaceService,
	deploymentService ports.DeploymentService,
	environmentService ports.EnvironmentService,
	serviceService ports.ServiceService,
	ingressService ports.IngressService,
	ingressSpecService ports.IngressSpecService,
	clusterService ports.ClusterService,
	orderService ports.OrderService,
	orderNamespaceService ports.OrderNamespaceService,
	domainService ports.DomainService,
) ports.ProjectService {
	return &ProjectService{
		namespaceService:      namespaceService,
		deploymentService:     deploymentService,
		environmentService:    environmentService,
		serviceService:        serviceService,
		ingressService:        ingressService,
		ingressSpecService:    ingressSpecService,
		clusterService:        clusterService,
		orderService:          orderService,
		orderNamespaceService: orderNamespaceService,
		domainService:         domainService,
	}
}

func (s *ProjectService) CreateProject(req *dto.CreateProjectRequest) (*dto.CreateProjectResponse, error) {
	// Step 1: Create namespace
	namespace, err := s.namespaceService.Create(req.Name, req.Slug, req.IsActive, req.Type, req.ClusterID)
	if err != nil {
		return nil, fmt.Errorf("failed to create namespace: %w", err)
	}

	// Step 2: Create auto-generated ingress with name {namespace}-ingress
	ingressName := fmt.Sprintf("%s-ingress", namespace.Name)
	ingress, err := s.ingressService.Create(ingressName, "nginx", namespace.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to create ingress: %w", err)
	}

	if req.Type == domain.NamespaceTypeDraft {
		envMasterDomain := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME")
		envMasterZoneID := os.Getenv("CLOUDFLARE_MASTER_ZONE_ID")
		envAccountID := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
		_, err = s.domainService.Create(
			envMasterDomain,
			true,
			true,
			envMasterZoneID,
			envAccountID,
			"master-account",
			namespace.ID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create domain: %w", err)
		}
	}

	var deploymentResponses []dto.CreateProjectDeploymentResponse

	// Step 3: Create deployments with their environments and services
	for _, deploymentReq := range req.Deployments {
		// Set default replicas if not specified
		replicas := deploymentReq.Replicas
		if replicas == 0 {
			replicas = 1
		}

		// Create deployment
		deployment, err := s.deploymentService.Create(
			deploymentReq.Name,
			deploymentReq.Image,
			deploymentReq.ContainerPort,
			replicas,
			namespace.ID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create deployment %s: %w", deploymentReq.Name, err)
		}

		var environmentResponses []dto.CreateProjectEnvironmentResponse
		// Create environments for this deployment
		for _, envReq := range deploymentReq.Environments {
			env, err := s.environmentService.Create(envReq.Name, envReq.Value, deployment.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to create environment %s for deployment %s: %w", envReq.Name, deploymentReq.Name, err)
			}
			environmentResponses = append(environmentResponses, dto.CreateProjectEnvironmentResponse{
				ID:    env.ID,
				Name:  env.Name,
				Value: env.Value,
			})
		}

		var serviceResponses []dto.CreateProjectServiceResponse
		// Create services for this deployment
		for _, serviceReq := range deploymentReq.Services {
			service, err := s.serviceService.Create(
				serviceReq.Name,
				serviceReq.Port,
				serviceReq.TargetPort,
				serviceReq.Type,
				"", // ClusterIP will be auto-assigned
				"", // ExternalIP
				namespace.ID,
				deployment.ID,
			)
			if err != nil {
				return nil, fmt.Errorf("failed to create service %s for deployment %s: %w", serviceReq.Name, deploymentReq.Name, err)
			}

			// Create ingress spec for this service (1:1 binding)
			masterDomain := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME")
			servicePort, err := strconv.ParseUint(serviceReq.Port, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("failed to parse service port %s: %w", serviceReq.Port, err)
			}
			namespaceIDStr := strconv.FormatUint(namespace.ID, 10)
			ingressSpec, err := s.ingressSpecService.Create(
				fmt.Sprintf("%s-%s-%s.%s", req.Name, namespaceIDStr, serviceReq.Name, masterDomain),
				"/",
				servicePort,
				service.ID,
				ingress.ID,
			)
			if err != nil {
				return nil, fmt.Errorf("failed to create ingress spec for service %s: %w", serviceReq.Name, err)
			}

			serviceResponses = append(serviceResponses, dto.CreateProjectServiceResponse{
				ID:         service.ID,
				CreatedAt:  service.CreatedAt,
				UpdatedAt:  service.UpdatedAt,
				Name:       service.Name,
				Port:       service.Port,
				TargetPort: service.TargetPort,
				Type:       service.Type,
				ClusterIP:  service.ClusterIP,
				ExternalIP: service.ExternalIP,
				IngressSpec: &dto.CreateProjectIngressSpecResponse{
					ID:   ingressSpec.ID,
					Host: ingressSpec.Host,
					Path: ingressSpec.Path,
					Port: ingressSpec.Port,
				},
			})
		}

		deploymentResponses = append(deploymentResponses, dto.CreateProjectDeploymentResponse{
			ID:            deployment.ID,
			CreatedAt:     deployment.CreatedAt,
			UpdatedAt:     deployment.UpdatedAt,
			Name:          deployment.Name,
			Image:         deployment.Image,
			ContainerPort: deployment.ContainerPort,
			Replicas:      deployment.Replicas,
			Environments:  environmentResponses,
			Services:      serviceResponses,
		})
	}

	// Step 4: Build response
	response := &dto.CreateProjectResponse{
		NamespaceDetailResponse: dto.ToNamespaceDetailDTO(namespace),
	}

	return response, nil
}

func (s *ProjectService) CreateProjectWithOrder(orderID uint64) (*dto.CreateProjectResponse, error) {
	order, err := s.orderService.GetByID(orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}
	project, err := s.CreateProjectWithTemplate(order.TemplateID, order.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to create project from template: %w", err)
	}
	_, err = s.orderNamespaceService.Create(orderID, project.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to create order namespace: %w", err)
	}
	return project, nil
}

func (s *ProjectService) CreateProjectWithTemplate(templateID uint64, name string) (*dto.CreateProjectResponse, error) {
	// Step 1: Validate input parameters
	if templateID == 0 {
		return nil, fmt.Errorf("template ID is required")
	}
	if name == "" {
		return nil, fmt.Errorf("project name is required")
	}

	// Step 2: Retrieve the template project
	template, err := s.namespaceService.GetByID(templateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template project: %w", err)
	}

	// Verify that the template is actually a template type
	if template.Type != domain.NamespaceTypeTemplate {
		return nil, fmt.Errorf("namespace with ID %d is not a template (type: %s)", templateID, template.Type)
	}

	// Step 3: Generate slug from the new name
	projectSlug := slug.Generate(name)
	if projectSlug == "" {
		return nil, fmt.Errorf("failed to generate valid slug from name: %s", name)
	}

	// Step 4: Build CreateProjectRequest from template data
	req := &dto.CreateProjectRequest{
		Name: name,
		Slug: projectSlug,
		//IsActive:  template.IsActive,
		IsActive:  false,
		Type:      domain.NamespaceTypeDraft, // Override to draft
		ClusterID: template.ClusterID,
	}

	// Copy deployments from template
	if template.Deployments != nil {
		for _, deployment := range template.Deployments {
			deploymentReq := dto.CreateProjectDeploymentRequest{
				Name:          deployment.Name,
				Image:         deployment.Image,
				ContainerPort: deployment.ContainerPort,
				Replicas:      deployment.Replicas,
			}

			// Copy environments for this deployment
			if deployment.Environments != nil {
				for _, env := range deployment.Environments {
					deploymentReq.Environments = append(deploymentReq.Environments, dto.CreateProjectEnvironmentRequest{
						Name:  env.Name,
						Value: env.Value,
					})
				}
			}

			// Copy services for this deployment by finding services that belong to this deployment
			if template.Services != nil {
				for _, service := range template.Services {
					if service.DeploymentID == deployment.ID {
						serviceReq := dto.CreateProjectServiceRequest{
							Name:       service.Name,
							Port:       service.Port,
							TargetPort: service.TargetPort,
							Type:       service.Type,
						}

						// Copy ingress spec for this service
						if service.IngressSpecs != nil && len(service.IngressSpecs) > 0 {
							// Use the first ingress spec as the template
							ingressSpec := service.IngressSpecs[0]
							serviceReq.IngressSpec = dto.CreateProjectIngressSpecRequest{
								Host: ingressSpec.Host,
								Path: ingressSpec.Path,
								Port: ingressSpec.Port,
							}
						}

						deploymentReq.Services = append(deploymentReq.Services, serviceReq)
					}
				}
			}

			req.Deployments = append(req.Deployments, deploymentReq)
		}
	}

	// Step 5: Create the new project using the existing CreateProject method
	return s.CreateProject(req)
}

func (s *ProjectService) UpdateProject(id uint64, req *dto.UpdateProjectRequest) (*dto.UpdateProjectResponse, error) {
	// Step 1: Update namespace
	namespace, err := s.namespaceService.Update(id, req.Name, req.Slug, req.IsActive, req.Type, req.ClusterID)
	if err != nil {
		return nil, fmt.Errorf("failed to update namespace: %w", err)
	}

	// Step 2: Delete all existing resources in the namespace

	// Get all existing deployments in the namespace
	deploymentFilter := &ports.DeploymentFilter{
		NamespaceID: &namespace.ID,
	}
	existingDeployments, err := s.deploymentService.GetAll(deploymentFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing deployments: %w", err)
	}

	// Delete environments and services for each deployment, then delete the deployment
	for _, deployment := range existingDeployments {
		// Delete environments for this deployment
		envFilter := &ports.EnvironmentFilter{
			DeploymentID: &deployment.ID,
		}
		existingEnvironments, err := s.environmentService.GetAll(envFilter)
		if err != nil {
			return nil, fmt.Errorf("failed to get existing environments for deployment %d: %w", deployment.ID, err)
		}

		for _, env := range existingEnvironments {
			if err := s.environmentService.Delete(env.ID); err != nil {
				return nil, fmt.Errorf("failed to delete environment %d: %w", env.ID, err)
			}
		}

		// Delete services for this deployment (and their ingress specs)
		serviceFilter := &ports.ServiceFilter{
			NamespaceID: &namespace.ID,
		}
		existingServices, err := s.serviceService.GetAll(serviceFilter)
		if err != nil {
			return nil, fmt.Errorf("failed to get existing services: %w", err)
		}

		for _, service := range existingServices {
			// Delete ingress specs for this service
			ingressSpecFilter := &ports.IngressSpecFilter{
				ServiceID: &service.ID,
			}
			existingIngressSpecs, err := s.ingressSpecService.GetAll(ingressSpecFilter)
			if err != nil {
				return nil, fmt.Errorf("failed to get existing ingress specs for service %d: %w", service.ID, err)
			}

			for _, ingressSpec := range existingIngressSpecs {
				if err := s.ingressSpecService.Delete(ingressSpec.ID); err != nil {
					return nil, fmt.Errorf("failed to delete ingress spec %d: %w", ingressSpec.ID, err)
				}
			}

			// Delete the service
			if err := s.serviceService.Delete(service.ID); err != nil {
				return nil, fmt.Errorf("failed to delete service %d: %w", service.ID, err)
			}
		}

		// Delete the deployment
		if err := s.deploymentService.Delete(deployment.ID); err != nil {
			return nil, fmt.Errorf("failed to delete deployment %d: %w", deployment.ID, err)
		}
	}

	// Delete existing ingresses in the namespace
	ingressFilter := &ports.IngressFilter{
		NamespaceID: &namespace.ID,
	}
	existingIngresses, err := s.ingressService.GetAll(ingressFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing ingresses: %w", err)
	}

	for _, ingress := range existingIngresses {
		if err := s.ingressService.Delete(ingress.ID); err != nil {
			return nil, fmt.Errorf("failed to delete ingress %d: %w", ingress.ID, err)
		}
	}

	// Step 3: Create new ingress with name {namespace}-ingress
	ingressName := fmt.Sprintf("%s-ingress", namespace.Name)
	ingress, err := s.ingressService.Create(ingressName, "nginx", namespace.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to create ingress: %w", err)
	}

	var deploymentResponses []dto.CreateProjectDeploymentResponse

	// Step 4: Create new deployments with their environments and services
	for _, deploymentReq := range req.Deployments {
		// Set default replicas if not specified
		replicas := deploymentReq.Replicas
		if replicas == 0 {
			replicas = 1
		}

		// Create deployment
		deployment, err := s.deploymentService.Create(
			deploymentReq.Name,
			deploymentReq.Image,
			deploymentReq.ContainerPort,
			replicas,
			namespace.ID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create deployment %s: %w", deploymentReq.Name, err)
		}

		var environmentResponses []dto.CreateProjectEnvironmentResponse
		// Create environments for this deployment
		for _, envReq := range deploymentReq.Environments {
			env, err := s.environmentService.Create(envReq.Name, envReq.Value, deployment.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to create environment %s for deployment %s: %w", envReq.Name, deploymentReq.Name, err)
			}
			environmentResponses = append(environmentResponses, dto.CreateProjectEnvironmentResponse{
				ID:    env.ID,
				Name:  env.Name,
				Value: env.Value,
			})
		}

		var serviceResponses []dto.CreateProjectServiceResponse
		// Create services for this deployment
		for _, serviceReq := range deploymentReq.Services {
			service, err := s.serviceService.Create(
				serviceReq.Name,
				serviceReq.Port,
				serviceReq.TargetPort,
				serviceReq.Type,
				"", // ClusterIP will be auto-assigned
				"", // ExternalIP
				namespace.ID,
				deployment.ID,
			)
			if err != nil {
				return nil, fmt.Errorf("failed to create service %s for deployment %s: %w", serviceReq.Name, deploymentReq.Name, err)
			}

			// Create ingress spec for this service (1:1 binding)
			ingressSpec, err := s.ingressSpecService.Create(
				serviceReq.IngressSpec.Host,
				serviceReq.IngressSpec.Path,
				serviceReq.IngressSpec.Port,
				service.ID,
				ingress.ID,
			)
			if err != nil {
				return nil, fmt.Errorf("failed to create ingress spec for service %s: %w", serviceReq.Name, err)
			}

			serviceResponses = append(serviceResponses, dto.CreateProjectServiceResponse{
				ID:         service.ID,
				CreatedAt:  service.CreatedAt,
				UpdatedAt:  service.UpdatedAt,
				Name:       service.Name,
				Port:       service.Port,
				TargetPort: service.TargetPort,
				Type:       service.Type,
				ClusterIP:  service.ClusterIP,
				ExternalIP: service.ExternalIP,
				IngressSpec: &dto.CreateProjectIngressSpecResponse{
					ID:   ingressSpec.ID,
					Host: ingressSpec.Host,
					Path: ingressSpec.Path,
					Port: ingressSpec.Port,
				},
			})
		}

		deploymentResponses = append(deploymentResponses, dto.CreateProjectDeploymentResponse{
			ID:            deployment.ID,
			CreatedAt:     deployment.CreatedAt,
			UpdatedAt:     deployment.UpdatedAt,
			Name:          deployment.Name,
			Image:         deployment.Image,
			ContainerPort: deployment.ContainerPort,
			Replicas:      deployment.Replicas,
			Environments:  environmentResponses,
			Services:      serviceResponses,
		})
	}

	// Step 5: Build response
	response := &dto.UpdateProjectResponse{
		NamespaceDetailResponse: dto.ToNamespaceDetailDTO(namespace),
	}

	return response, nil
}

func (s *ProjectService) GetAllProjects(filter *ports.ProjectFilter) ([]*dto.ProjectListItemResponse, error) {
	// Build namespace filter from project filter
	namespaceFilter := &ports.NamespaceFilter{}

	// Apply filters if provided
	if filter != nil {
		if filter.IsActive != nil {
			namespaceFilter.IsActive = filter.IsActive
		}
		if filter.ClusterID != nil {
			namespaceFilter.ClusterID = filter.ClusterID
		}
		if filter.Type != nil {
			namespaceFilter.Type = filter.Type
		}
		if filter.Name != nil {
			namespaceFilter.Name = filter.Name
		}
		if filter.Slug != nil {
			namespaceFilter.Slug = filter.Slug
		}
	}

	// Handle workspace filter by getting clusters from workspace
	var allowedClusterIDs []uint64
	if filter != nil && filter.WorkspaceID != nil {
		// Get clusters for the workspace
		clusterFilter := &ports.ClusterFilter{
			WorkspaceID: filter.WorkspaceID,
		}
		clusters, err := s.clusterService.GetAll(clusterFilter)
		if err != nil {
			return nil, fmt.Errorf("failed to get clusters for workspace: %w", err)
		}

		// Extract cluster IDs
		for _, cluster := range clusters {
			allowedClusterIDs = append(allowedClusterIDs, cluster.ID)
		}

		// If no clusters found for workspace, return empty result
		if len(allowedClusterIDs) == 0 {
			return []*dto.ProjectListItemResponse{}, nil
		}
	}

	namespaces, err := s.namespaceService.GetAll(namespaceFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespaces: %w", err)
	}

	// Filter for project type namespaces and apply filters
	var projects []*dto.ProjectListItemResponse
	for _, namespace := range namespaces {

		// Apply workspace filter by checking if namespace's cluster is in allowed clusters
		if len(allowedClusterIDs) > 0 {
			isAllowed := false
			for _, allowedClusterID := range allowedClusterIDs {
				if namespace.ClusterID == allowedClusterID {
					isAllowed = true
					break
				}
			}
			if !isAllowed {
				continue
			}
		}

		project := dto.ToProjectListItemDTO(namespace)
		projects = append(projects, project)
	}

	return projects, nil
}

func (s *ProjectService) GetByID(id uint64) (*dto.ProjectDetailResponse, error) {
	namespace, err := s.namespaceService.GetByID(id)
	if err != nil {
		return nil, err
	}

	return dto.ToProjectDetailDTO(namespace), nil
}
