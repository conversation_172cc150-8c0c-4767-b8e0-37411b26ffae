variable "cloudflare_api_token" {
  type = string
  default = ""
}

// for dns records
variable "cloudflare_zone_id" {
  type = string
  default = ""
}

variable "ip_address" {
  type = string
  default = ""
}

variable "operation_endpoint" {
  type = string
  default = ""
}

variable "access_token" {
  type = string
  default = ""
}

variable "namespace_status" {
  type = string
  default = "inactive"
}

data "http" "domains" {
  url = var.operation_endpoint
  method = "GET"
  request_headers = {
    Accept = "application/json"
    Authorization = "Bearer ${var.access_token}"
  }
}